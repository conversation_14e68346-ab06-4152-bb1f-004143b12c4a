import cv2
import time
import threading
from queue import Queue
from hand_tracker import Hand<PERSON><PERSON>
from gesture_manager import GestureManager
from virtual_keyboard import VirtualKeyboard
from utils import debounce, draw_overlay, calibrate_threshold, set_window_always_on_top
from config import Config
import logging

logging.basicConfig(filename="logs/app.log", level=logging.INFO)

def process_frame(tracker, gesture_mgr, keyboard, cap, frame_queue, feedback_queue):
    last_action_time = 0
    frame_time = 1 / Config.FPS

    while cap.isOpened():
        start_time = time.time()
        ret, frame = cap.read()
        if not ret:
            logging.error("Failed to capture frame.")
            break

        frame = cv2.flip(frame, 1)
        logging.info(f"Raw frame size before resize: {frame.shape}")
        frame = cv2.resize(frame, (Config.WINDOW_WIDTH, Config.WINDOW_HEIGHT))
        logging.info(f"Processed frame size: {frame.shape}")
        results = tracker.process_frame(frame)
        feedback = {"type": "none", "gesture": "none"}

        if results and results.multi_hand_landmarks:
            right_hand_detected = False
            for idx, (hand_landmarks, handedness) in enumerate(zip(results.multi_hand_landmarks, results.multi_handedness)):
                hand_label = handedness.classification[0].label.lower()
                if hand_label == "right" and not right_hand_detected:
                    tracker.draw_landmarks(frame, hand_landmarks, idx, raw=True)
                    if gesture_mgr.mode == "mouse":
                        feedback = gesture_mgr.handle_gestures(frame, hand_landmarks, tracker, hand_label, idx)
                        if feedback["type"] in ["left_click", "right_click", "scroll_up", "scroll_down", "volume_up", "volume_down"]:
                            can_act, last_action_time = debounce(last_action_time)
                            if not can_act:
                                feedback = {"type": "none", "gesture": feedback["gesture"]}
                    elif gesture_mgr.mode == "keyboard":
                        key_action = keyboard.detect_key_press(frame, hand_landmarks, tracker, idx)
                        if key_action:
                            logging.info(f"Key pressed: {key_action}")
                    right_hand_detected = True
                elif hand_label == "left":
                    feedback = gesture_mgr.handle_gestures(frame, hand_landmarks, tracker, hand_label, idx)
                    if feedback["type"].startswith("mode"):
                        can_act, last_action_time = debounce(last_action_time)
                        if not can_act:
                            feedback = {"type": "none", "gesture": feedback["gesture"]}
                        else:
                            logging.info(f"Mode switched to: {gesture_mgr.mode}")

        if gesture_mgr.mode == "keyboard":
            keyboard.draw_keyboard(frame)
        frame_queue.put(frame)
        feedback_queue.put(feedback)

        elapsed = time.time() - start_time
        if elapsed < frame_time:
            time.sleep(frame_time - elapsed)

def main():
    logging.info("Starting Gesture Controller...")
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        logging.error("Could not open webcam.")
        print("Error: Could not open webcam.")
        return

    # Force 640x480 for consistency
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    if width == 0 or height == 0:
        width, height = 640, 480
    Config.WINDOW_WIDTH = width
    Config.WINDOW_HEIGHT = height
    logging.info(f"Webcam resolution: {width}x{height}")
    print(f"Webcam resolution set to: {width}x{height}")

    tracker = HandTracker()
    gesture_mgr = GestureManager()
    keyboard = VirtualKeyboard()
    calibrate_threshold(tracker, cap)

    frame_queue = Queue(maxsize=1)
    feedback_queue = Queue(maxsize=1)
    processing_thread = threading.Thread(target=process_frame, args=(tracker, gesture_mgr, keyboard, cap, frame_queue, feedback_queue))
    processing_thread.daemon = True
    processing_thread.start()

    window_name = "Gesture Controller"
    cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
    cv2.resizeWindow(window_name, Config.WINDOW_WIDTH, Config.WINDOW_HEIGHT)
    print("Debug mode is ON. Watch 'Gesture: ...' text. Press 'd' to toggle debug, 'q' to quit, 'c' to recalibrate.")
    while True:
        if not frame_queue.empty():
            frame = frame_queue.get()
            feedback = feedback_queue.get()
            draw_overlay(frame, feedback)
            cv2.imshow(window_name, frame)
            set_window_always_on_top(window_name)

        key = cv2.waitKey(1)
        if key & 0xFF == ord('q'):
            logging.info("Shutting down...")
            break
        elif key & 0xFF == ord('c'):
            calibrate_threshold(tracker, cap)
        elif key & 0xFF == ord('d'):
            Config.DEBUG_MODE = not Config.DEBUG_MODE
            logging.info(f"Debug mode: {Config.DEBUG_MODE}")
            print(f"Debug mode: {Config.DEBUG_MODE}")

    cap.release()
    cv2.destroyAllWindows()
    tracker.close()

if __name__ == "__main__":
    main()