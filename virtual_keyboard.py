import pyautogui
import json
import cv2
import time
from hand_tracker import Hand<PERSON><PERSON>
from config import Config
import logging

class VirtualKeyboard:
    def __init__(self, layout_file="assets/keyboard_layout.json", frame_width=Config.WINDOW_WIDTH):
        self.load_layout(layout_file, frame_width or Config.WINDOW_WIDTH or 640)
        self.active_key = None
        self.hovered_key = None
        self.shift_active = False
        self.ctrl_active = False
        self.alt_active = False
        self.last_pinch_time = 0

    def load_layout(self, layout_file, frame_width):
        try:
            with open(layout_file, 'r') as f:
                base_layout = json.load(f)
        except FileNotFoundError:
            base_layout = json.loads('''
{
  "q": {"row": 0, "col": 0},
  "w": {"row": 0, "col": 1},
  "e": {"row": 0, "col": 2},
  "r": {"row": 0, "col": 3},
  "t": {"row": 0, "col": 4},
  "y": {"row": 0, "col": 5},
  "u": {"row": 0, "col": 6},
  "i": {"row": 0, "col": 7},
  "o": {"row": 0, "col": 8},
  "p": {"row": 0, "col": 9},
  "backspace": {"row": 0, "col": 10, "width": 2},
  "a": {"row": 1, "col": 0.5},
  "s": {"row": 1, "col": 1.5},
  "d": {"row": 1, "col": 2.5},
  "f": {"row": 1, "col": 3.5},
  "g": {"row": 1, "col": 4.5},
  "h": {"row": 1, "col": 5.5},
  "j": {"row": 1, "col": 6.5},
  "k": {"row": 1, "col": 7.5},
  "l": {"row": 1, "col": 8.5},
  "tab": {"row": 1, "col": 9.5, "width": 1.5},
  "z": {"row": 2, "col": 0},
  "x": {"row": 2, "col": 1},
  "c": {"row": 2, "col": 2},
  "v": {"row": 2, "col": 3},
  "b": {"row": 2, "col": 4},
  "n": {"row": 2, "col": 5},
  "m": {"row": 2, "col": 6},
  "shift": {"row": 2, "col": 7, "width": 1.5, "action": "hold"},
  "ctrl": {"row": 2, "col": 8.5, "width": 1.5, "action": "hold"},
  "alt": {"row": 2, "col": 10, "width": 1.5, "action": "hold"},
  "space": {"row": 2, "col": 11.5, "width": 2},
  "enter": {"row": 2, "col": 13.5, "width": 2}
}
            ''')

        self.key_layout = {}
        key_size = Config.KEY_SIZE
        spacing = Config.KEY_SPACING

        max_col = max(info["col"] + info.get("width", 1) for info in base_layout.values())
        max_row = max(info["row"] for info in base_layout.values())
        total_width = max_col * (key_size + spacing)
        total_height = (max_row + 1) * (key_size + spacing)
        # Scale to fit frame width
        scale = min(1.0, (frame_width - 10) / total_width) if total_width > 0 else 1.0
        adjusted_key_size = int(key_size * scale)
        adjusted_spacing = int(spacing * scale)
        total_width = max_col * (adjusted_key_size + adjusted_spacing)
        total_height = (max_row + 1) * (adjusted_key_size + adjusted_spacing)
        start_x = (frame_width - total_width) // 2
        start_y = (Config.WINDOW_HEIGHT - total_height) // 2
        logging.info(f"Keyboard dimensions: {total_width}x{total_height}, scale: {scale}, start_x: {start_x}, start_y: {start_y}")

        for key, info in base_layout.items():
            width = info.get("width", 1) * adjusted_key_size + (info.get("width", 1) - 1) * adjusted_spacing
            x = int(start_x + info["col"] * (adjusted_key_size + adjusted_spacing))
            y = int(start_y + info["row"] * (adjusted_key_size + adjusted_spacing))
            self.key_layout[key] = {
                "x": x,
                "y": y,
                "width": width,
                "height": adjusted_key_size,
                "action": info.get("action", "press")
            }
            logging.info(f"Key {key} position: ({x}, {y}, {width}, {adjusted_key_size})")

    def detect_key_press(self, frame, hand_landmarks, tracker: HandTracker, hand_idx=0):
        try:
            index_x, index_y = tracker.get_landmark_position(frame, hand_landmarks, 8, hand_idx)
            self.hovered_key = None
            self.active_key = None

            gesture = tracker.recognize_gesture(frame, hand_landmarks, hand_idx)
            if gesture == "pinch_thumb_index":
                current_time = time.time()
                if current_time - self.last_pinch_time >= Config.KEY_PINCH_COOLDOWN:
                    for key, pos in self.key_layout.items():
                        x, y, w, h = pos["x"], pos["y"], pos["width"], pos["height"]
                        if x <= index_x <= x + w and y <= index_y <= y + h:
                            self.hovered_key = self.active_key = key
                            if key == "shift":
                                self.shift_active = True
                                pyautogui.keyDown("shift")
                            elif key == "ctrl":
                                self.ctrl_active = True
                                pyautogui.keyDown("ctrl")
                            elif key == "alt":
                                self.alt_active = True
                                pyautogui.keyDown("alt")
                            elif key in ["backspace", "space", "tab", "enter"]:
                                pyautogui.press(key)
                            else:
                                pyautogui.press(key.upper() if self.shift_active else key)
                            self.last_pinch_time = current_time
                            break
                    else:
                        if self.shift_active and self.active_key != "shift":
                            pyautogui.keyUp("shift")
                            self.shift_active = False
                        if self.ctrl_active and self.active_key != "ctrl":
                            pyautogui.keyUp("ctrl")
                            self.ctrl_active = False
                        if self.alt_active and self.active_key != "alt":
                            pyautogui.keyUp("alt")
                            self.alt_active = False

            return self.active_key
        except Exception as e:
            logging.error(f"Key detection error: {e}")
            return None

    def draw_keyboard(self, frame):
        try:
            logging.info(f"Frame size in draw_keyboard: {frame.shape}")
            for key, pos in self.key_layout.items():
                x, y, w, h = pos["x"], pos["y"], pos["width"], pos["height"]
                if y + h > frame.shape[0] or x + w > frame.shape[1] or y < 0 or x < 0:
                    logging.warning(f"Key {key} out of bounds: ({x}, {y}, {w}, {h}) vs frame {frame.shape}")
                    continue
                color = (0, 255, 0) if key != self.active_key else (0, 0, 255)
                if key == self.hovered_key:
                    color = (255, 255, 0)
                cv2.rectangle(frame, (x, y), (x + w, y + h), color, 4)  # Thicker outline
                font_scale = min(1.0, w / 20)  # Larger font
                cv2.putText(frame, key.upper() if self.shift_active and key.isalpha() else key,
                            (x + 5, y + h - 5), cv2.FONT_HERSHEY_SIMPLEX, font_scale, (255, 255, 255), 4)
        except Exception as e:
            logging.error(f"Keyboard drawing error: {e}")