import pyautogui
from hand_tracker import HandTracker
from config import Config
import logging

class GestureManager:
    def __init__(self):
        Config.update_screen_size()
        self.prev_mouse_x, self.prev_mouse_y = None, None
        self.smoothing_factor = Config.SMOOTHING_FACTOR
        self.mode = "mouse"
        self.gestures = Config.load_gestures()

    def move_mouse(self, frame, hand_landmarks, tracker: HandTracker, hand_idx=0):
        try:
            index_x, index_y = tracker.get_landmark_position(frame, hand_landmarks, 8, hand_idx)
            mouse_x = int(index_x * Config.SCREEN_WIDTH / frame.shape[1] * 1.5)
            mouse_y = int(index_y * Config.SCREEN_HEIGHT / frame.shape[0] * 1.5)
            if self.prev_mouse_x is None:
                self.prev_mouse_x, self.prev_mouse_y = mouse_x, mouse_y
            smoothed_x = int(self.smoothing_factor * mouse_x + (1 - self.smoothing_factor) * self.prev_mouse_x)
            smoothed_y = int(self.smoothing_factor * mouse_y + (1 - self.smoothing_factor) * self.prev_mouse_y)
            pyautogui.moveTo(smoothed_x, smoothed_y)
            self.prev_mouse_x, self.prev_mouse_y = smoothed_x, smoothed_y
            return {"type": "move", "x": smoothed_x, "y": smoothed_y}
        except Exception as e:
            logging.error(f"Mouse move error: {e}")
            return {"type": "error"}

    def handle_gestures(self, frame, hand_landmarks, tracker: HandTracker, hand_label, hand_idx=0):
        try:
            gesture = tracker.recognize_gesture(frame, hand_landmarks, hand_idx)
            if not gesture:
                return {"type": "none", "gesture": "none"}

            gesture_map = self.gestures.get(hand_label, {})
            action = gesture_map.get(gesture, "none")
            feedback = {"type": action, "gesture": gesture}

            if hand_label == "right":
                if self.mode == "mouse":
                    if action == "move_cursor":
                        return self.move_mouse(frame, hand_landmarks, tracker, hand_idx)
                    elif action == "left_click":
                        pyautogui.click()
                    elif action == "right_click":
                        pyautogui.click(button="right")
                    elif action == "scroll_up":
                        pyautogui.scroll(30)
                    elif action == "scroll_down":
                        pyautogui.scroll(-30)
                    elif action == "volume_up":
                        pyautogui.hotkey('volumeup')
                    elif action == "volume_down":
                        pyautogui.hotkey('volumedown')
            elif hand_label == "left":
                if action == "toggle_mode":
                    self.mode = "keyboard" if self.mode == "mouse" else "mouse"
                    feedback["type"] = f"mode_{self.mode}"

            return feedback
        except Exception as e:
            logging.error(f"Gesture handling error: {e}")
            return {"type": "error", "gesture": "error"}