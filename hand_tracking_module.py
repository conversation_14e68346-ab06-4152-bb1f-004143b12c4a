import cv2
import mediapipe as mp
from config import Config

class HandTracker:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            max_num_hands=Config.MAX_HANDS,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_draw = mp.solutions.drawing_utils
        self.landmark_history = [None] * Config.MAX_HANDS
        self.smoothing_factor = Config.SMOOTHING_FACTOR

    def process_frame(self, frame):
        try:
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(frame_rgb)
            return results
        except Exception as e:
            print(f"Error processing frame: {e}")
            return None

    def draw_landmarks(self, frame, hand_landmarks):
        self.mp_draw.draw_landmarks(frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS)

    def get_landmark_position(self, frame, hand_landmarks, landmark_id, hand_idx=0):
        h, w, _ = frame.shape
        landmark = hand_landmarks.landmark[landmark_id]
        x, y = landmark.x * w, landmark.y * h
        if self.landmark_history[hand_idx] is None:
            self.landmark_history[hand_idx] = [(x, y) for _ in range(21)]
        smoothed_x = self.smoothing_factor * x + (1 - self.smoothing_factor) * self.landmark_history[hand_idx][landmark_id][0]
        smoothed_y = self.smoothing_factor * y + (1 - self.smoothing_factor) * self.landmark_history[hand_idx][landmark_id][1]
        self.landmark_history[hand_idx][landmark_id] = (smoothed_x, smoothed_y)
        return int(smoothed_x), int(smoothed_y)

    def recognize_gesture(self, frame, hand_landmarks):
        thumb_x, thumb_y = self.get_landmark_position(frame, hand_landmarks, 4)
        index_x, index_y = self.get_landmark_position(frame, hand_landmarks, 8)
        middle_x, middle_y = self.get_landmark_position(frame, hand_landmarks, 12)
        ring_x, ring_y = self.get_landmark_position(frame, hand_landmarks, 16)
        pinky_x, pinky_y = self.get_landmark_position(frame, hand_landmarks, 20)
        wrist_y = self.get_landmark_position(frame, hand_landmarks, 0)[1]

        # Pinch (thumb + index)
        pinch_dist = ((thumb_x - index_x) ** 2 + (thumb_y - index_y) ** 2) ** 0.5
        if pinch_dist < Config.PINCH_THRESHOLD:
            return "pinch"

        # Open hand (all fingers spread)
        fingers = [(index_x, index_y), (middle_x, middle_y), (ring_x, ring_y), (pinky_x, pinky_y)]
        spread = all(((f1[0] - f2[0]) ** 2 + (f1[1] - f2[1]) ** 2) ** 0.5 > Config.SPREAD_THRESHOLD for i, f1 in enumerate(fingers) for f2 in fingers[i+1:])
        if spread:
            return "open"

        # Point (index up, others down)
        if index_y < wrist_y - Config.POINT_THRESHOLD and all(f[0] > wrist_y - 50 for f in [(middle_y,), (ring_y,), (pinky_y,)]):
            return "point"

        # Two fingers (index + middle up)
        if index_y < wrist_y - 80 and middle_y < wrist_y - 80 and ring_y > wrist_y - 50:
            return "two_fingers"

        # Three fingers (index + middle + ring up)
        if index_y < wrist_y - 80 and middle_y < wrist_y - 80 and ring_y < wrist_y - 80:
            return "three_fingers"

        return None

    def close(self):
        self.hands.close()