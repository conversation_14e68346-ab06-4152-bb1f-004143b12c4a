import pyautogui
from hand_tracking_module import HandTracker
from config import Config

class MouseController:
    def __init__(self):
        self.screen_width, self.screen_height = pyautogui.size()
        self.prev_mouse_x, self.prev_mouse_y = None, None
        self.smoothing_factor = Config.MOUSE_SMOOTHING
        self.is_dragging = False

    def move_mouse(self, frame, hand_landmarks, tracker: HandTracker, hand_idx=0):
        index_x, index_y = tracker.get_landmark_position(frame, hand_landmarks, 8, hand_idx)
        mouse_x = int(index_x * self.screen_width / frame.shape[1])
        mouse_y = int(index_y * self.screen_height / frame.shape[0])

        if self.prev_mouse_x is None:
            self.prev_mouse_x, self.prev_mouse_y = mouse_x, mouse_y
        smoothed_x = int(self.smoothing_factor * mouse_x + (1 - self.smoothing_factor) * self.prev_mouse_x)
        smoothed_y = int(self.smoothing_factor * mouse_y + (1 - self.smoothing_factor) * self.prev_mouse_y)
        pyautogui.moveTo(smoothed_x, smoothed_y)
        self.prev_mouse_x, self.prev_mouse_y = smoothed_x, smoothed_y
        return {"type": "move", "x": smoothed_x, "y": smoothed_y}

    def handle_gestures(self, frame, hand_landmarks, tracker: HandTracker, hand_idx=0):
        gesture = tracker.recognize_gesture(frame, hand_landmarks)
        feedback = {"type": gesture}

        if gesture == "point":  # Move cursor
            self.move_mouse(frame, hand_landmarks, tracker, hand_idx)
            if self.is_dragging:
                pyautogui.mouseUp()
                self.is_dragging = False

        elif gesture == "pinch":  # Click or start drag
            if not self.is_dragging:
                pyautogui.mouseDown()
                self.is_dragging = True
            self.move_mouse(frame, hand_landmarks, tracker, hand_idx)
            feedback["type"] = "click"

        elif gesture == "open":  # Release drag or scroll
            if self.is_dragging:
                pyautogui.mouseUp()
                self.is_dragging = False
            else:
                index_y = tracker.get_landmark_position(frame, hand_landmarks, 8, hand_idx)[1]
                middle_y = tracker.get_landmark_position(frame, hand_landmarks, 12, hand_idx)[1]
                if index_y < middle_y - 50:
                    pyautogui.scroll(Config.SCROLL_SPEED)
                    feedback["type"] = "scroll_up"
                elif middle_y < index_y - 50:
                    pyautogui.scroll(-Config.SCROLL_SPEED)
                    feedback["type"] = "scroll_down"

        return feedback