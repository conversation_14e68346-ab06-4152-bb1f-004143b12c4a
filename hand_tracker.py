import cv2
import mediapipe as mp
from config import Config
import logging

logging.basicConfig(filename="logs/app.log", level=logging.INFO)

class HandTracker:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            max_num_hands=Config.MAX_HANDS,
            min_detection_confidence=0.8,
            min_tracking_confidence=0.7
        )
        self.mp_draw = mp.solutions.drawing_utils
        self.landmark_history = [None] * Config.MAX_HANDS
        self.smoothing_factor = Config.SMOOTHING_FACTOR
        self.prev_y = [None] * Config.MAX_HANDS  # For swipe detection

    def process_frame(self, frame):
        try:
            self.original_h, self.original_w = frame.shape[:2]
            small_frame = cv2.resize(frame, None, fx=Config.FRAME_SCALE, fy=Config.FRAME_SCALE)
            frame_rgb = cv2.cvtColor(small_frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(frame_rgb)
            return results
        except Exception as e:
            logging.error(f"Frame processing error: {e}")
            return None

    def draw_landmarks(self, frame, hand_landmarks, hand_idx=0, raw=False):
        try:
            if raw and Config.DEBUG_MODE:
                for lm in hand_landmarks.landmark:
                    x, y = int(lm.x * self.original_w), int(lm.y * self.original_h)
                    cv2.circle(frame, (x, y), 5, (255, 0, 0), -1)
            self.mp_draw.draw_landmarks(frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS)
        except Exception as e:
            logging.error(f"Landmark drawing error: {e}")

    def get_landmark_position(self, frame, hand_landmarks, landmark_id, hand_idx=0):
        h, w, _ = frame.shape
        landmark = hand_landmarks.landmark[landmark_id]
        x, y = landmark.x * w, landmark.y * h
        if self.landmark_history[hand_idx] is None:
            self.landmark_history[hand_idx] = [(x, y) for _ in range(21)]
        smoothed_x = self.smoothing_factor * x + (1 - self.smoothing_factor) * self.landmark_history[hand_idx][landmark_id][0]
        smoothed_y = self.smoothing_factor * y + (1 - self.smoothing_factor) * self.landmark_history[hand_idx][landmark_id][1]
        self.landmark_history[hand_idx][landmark_id] = (smoothed_x, smoothed_y)
        return int(smoothed_x), int(smoothed_y)

    def recognize_gesture(self, frame, hand_landmarks, hand_idx=0):
        try:
            thumb_x, thumb_y = self.get_landmark_position(frame, hand_landmarks, 4, hand_idx)
            index_x, index_y = self.get_landmark_position(frame, hand_landmarks, 8, hand_idx)
            middle_x, middle_y = self.get_landmark_position(frame, hand_landmarks, 12, hand_idx)
            ring_x, ring_y = self.get_landmark_position(frame, hand_landmarks, 16, hand_idx)
            pinky_x, pinky_y = self.get_landmark_position(frame, hand_landmarks, 20, hand_idx)
            wrist_y = self.get_landmark_position(frame, hand_landmarks, 0, hand_idx)[1]

            # Pinch (thumb + index for left click)
            pinch_thumb_index_dist = ((thumb_x - index_x) ** 2 + (thumb_y - index_y) ** 2) ** 0.5
            if pinch_thumb_index_dist < Config.PINCH_THRESHOLD:
                return "pinch_thumb_index"

            # Pinch (thumb + any other finger for right click: middle, ring, or pinky)
            pinch_thumb_middle_dist = ((thumb_x - middle_x) ** 2 + (thumb_y - middle_y) ** 2) ** 0.5
            pinch_thumb_ring_dist = ((thumb_x - ring_x) ** 2 + (thumb_y - ring_y) ** 2) ** 0.5
            pinch_thumb_pinky_dist = ((thumb_x - pinky_x) ** 2 + (thumb_y - pinky_y) ** 2) ** 0.5
            if any(dist < Config.PINCH_THRESHOLD for dist in [pinch_thumb_middle_dist, pinch_thumb_ring_dist, pinch_thumb_pinky_dist]):
                return "pinch_thumb_other"

            # One finger (index up, others down)
            finger_y = [index_y, middle_y, ring_y, pinky_y]
            if index_y < wrist_y - Config.ONE_FINGER_THRESHOLD and all(y >= wrist_y - 40 for y in finger_y[1:]):
                return "one_finger"

            # Volume Up (thumb up, others down)
            if thumb_y < wrist_y - Config.FINGER_UP_THRESHOLD and all(y >= wrist_y - 40 for y in finger_y):
                return "thumb_up"

            # Volume Down (pinky up, others down)
            if pinky_y < wrist_y - Config.FINGER_UP_THRESHOLD and all(y >= wrist_y - 40 for y in [thumb_y, index_y, middle_y, ring_y]):
                return "pinky_up"

            # Swipe (up/down for scroll)
            current_y = sum(finger_y) / len(finger_y)
            if self.prev_y[hand_idx] is not None:
                delta_y = self.prev_y[hand_idx] - current_y
                if abs(delta_y) > Config.SWIPE_THRESHOLD:
                    self.prev_y[hand_idx] = current_y
                    return "swipe_up" if delta_y > 0 else "swipe_down"
            self.prev_y[hand_idx] = current_y

            return None
        except Exception as e:
            logging.error(f"Gesture recognition error: {e}")
            return None

    def close(self):
        try:
            self.hands.close()
        except Exception as e:
            logging.error(f"Hand tracker close error: {e}")