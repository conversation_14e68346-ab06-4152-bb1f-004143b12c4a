import json

class Config:
    MAX_HANDS = 2
    FPS = 60
    SMOOTHING_FACTOR = 0.5
    DEBOUNCE_DELAY = 0.15
    FRAME_SCALE = 1.0
    SCREEN_WIDTH, SCREEN_HEIGHT = None, None
    DEBUG_MODE = True

    PINCH_THRESHOLD = 55  # Increased for larger keys
    OPEN_THRESHOLD = 70
    ONE_FINGER_THRESHOLD = 80
    SWIPE_THRESHOLD = 40
    FINGER_UP_THRESHOLD = 80

    KEY_SIZE = 40  # Increased from 30
    KEY_SPACING = 4
    KEYBOARD_Y_OFFSET = 0
    KEY_PINCH_COOLDOWN = 0.15

    OVERLAY_OPACITY = 0.6
    WINDOW_X, WINDOW_Y = 50, 50
    WINDOW_WIDTH, WINDOW_HEIGHT = 640, 480

    @staticmethod
    def load_gestures(file="assets/gestures.json"):
        try:
            with open(file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return {
                "right": {
                    "one_finger": "move_cursor",
                    "pinch_thumb_index": "left_click",
                    "pinch_thumb_other": "right_click",
                    "swipe_up": "scroll_up",
                    "swipe_down": "scroll_down",
                    "thumb_up": "volume_up",
                    "pinky_up": "volume_down"
                },
                "left": {
                    "pinch_thumb_index": "toggle_mode"
                }
            }

    @staticmethod
    def update_screen_size():
        import pyautogui
        Config.SCREEN_WIDTH, Config.SCREEN_HEIGHT = pyautogui.size()