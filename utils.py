import cv2
import time
import pygame
import logging
import win32gui
import win32con
from config import Config
from hand_tracker import HandTracker

logging.basicConfig(filename="logs/app.log", level=logging.INFO)
pygame.mixer.init()

def debounce(last_action_time, delay=Config.DEBOUNCE_DELAY):
    current_time = time.time()
    if current_time - last_action_time >= delay:
        return True, current_time
    return False, last_action_time

def draw_overlay(frame, feedback):
    try:
        overlay = frame.copy()
        h, w = frame.shape[:2]
        if feedback["type"] == "move":
            cv2.circle(overlay, (feedback["x"] * w // Config.SCREEN_WIDTH, feedback["y"] * h // Config.SCREEN_HEIGHT), 8, (0, 255, 255), -1)
        elif feedback["type"] in ["left_click", "right_click"]:
            cv2.putText(overlay, feedback["type"].replace("_", " "), (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            try:
                pygame.mixer.Sound("assets/click.wav").play()
            except:
                pass
        elif feedback["type"] in ["scroll_up", "scroll_down"]:
            cv2.putText(overlay, feedback["type"].replace("_", " "), (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
            try:
                pygame.mixer.Sound("assets/scroll.wav").play()
            except:
                pass
        elif feedback["type"].startswith("mode"):
            cv2.putText(overlay, f"Mode: {feedback['type'].split('_')[1]}", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        elif feedback["type"] in ["volume_up", "volume_down"]:
            cv2.putText(overlay, feedback["type"].replace("_", " "), (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        # Enhanced debug info
        cv2.putText(overlay, f"Gesture: {feedback['gesture']}", (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        if Config.DEBUG_MODE:
            cv2.putText(overlay, "DEBUG MODE: Blue = Raw, Green = Smoothed", (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
            cv2.putText(overlay, "Try: Pinch (thumb+index), Pinch (index+middle), Index up", (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        cv2.addWeighted(overlay, Config.OVERLAY_OPACITY, frame, 1 - Config.OVERLAY_OPACITY, 0, frame)
    except Exception as e:
        logging.error(f"Overlay drawing error: {e}")

def calibrate_threshold(tracker: 'HandTracker', cap):
    logging.info("Starting calibration...")
    print("Calibration: Hold a pinch (thumb + index) with your right hand for 3 seconds...")
    start_time = time.time()
    distances = []

    while time.time() - start_time < 3:
        ret, frame = cap.read()
        if not ret:
            continue
        frame = cv2.flip(frame, 1)
        results = tracker.process_frame(frame)
        if results and results.multi_hand_landmarks:
            for idx, (hand_landmarks, handedness) in enumerate(zip(results.multi_hand_landmarks, results.multi_handedness)):
                if handedness.classification[0].label.lower() == "right":
                    thumb_x, thumb_y = tracker.get_landmark_position(frame, hand_landmarks, 4, idx)
                    index_x, index_y = tracker.get_landmark_position(frame, hand_landmarks, 8, idx)
                    dist = ((thumb_x - index_x) ** 2 + (thumb_y - index_y) ** 2) ** 0.5
                    distances.append(dist)
        cv2.imshow("Calibration", frame)
        cv2.waitKey(1)

    if distances:
        Config.PINCH_THRESHOLD = int(sum(distances) / len(distances) * 1.1)
        logging.info(f"Calibrated pinch threshold: {Config.PINCH_THRESHOLD}")
        print(f"Calibrated pinch threshold: {Config.PINCH_THRESHOLD}")
    else:
        logging.warning("Calibration failed: No right hand detected.")
        print("Calibration failed: No right hand detected.")
    cv2.destroyWindow("Calibration")

def set_window_always_on_top(window_name):
    try:
        hwnd = win32gui.FindWindow(None, window_name)
        if hwnd:
            win32gui.SetWindowPos(hwnd, win32con.HWND_TOPMOST, Config.WINDOW_X, Config.WINDOW_Y, 0, 0, win32con.SWP_NOSIZE)
    except Exception as e:
        logging.error(f"Window management error: {e}")